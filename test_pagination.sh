#!/bin/bash

# 分页功能测试脚本

BASE_URL="http://localhost:8080/glc"
ENDPOINT="/v1/log/search"

echo "=== 分页功能测试 ==="
echo

# 测试第1页
echo "1. 测试第1页（默认）:"
curl -s -X POST "${BASE_URL}${ENDPOINT}" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "searchKey=&pagesize=5" | jq -r '.result.data[0].id // "无数据"'
echo

# 测试第1页（显式指定）
echo "2. 测试第1页（显式指定 pagenum=1）:"
curl -s -X POST "${BASE_URL}${ENDPOINT}" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "searchKey=&pagenum=1&pagesize=5" | jq -r '.result.data[0].id // "无数据"'
echo

# 测试第2页
echo "3. 测试第2页:"
curl -s -X POST "${BASE_URL}${ENDPOINT}" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "searchKey=&pagenum=2&pagesize=5" | jq -r '.result.data[0].id // "无数据"'
echo

# 测试第3页
echo "4. 测试第3页:"
curl -s -X POST "${BASE_URL}${ENDPOINT}" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "searchKey=&pagenum=3&pagesize=5" | jq -r '.result.data[0].id // "无数据"'
echo

# 详细测试第1页和第3页的数据对比
echo "5. 详细对比第1页和第3页的数据:"
echo "第1页前3条记录的ID:"
curl -s -X POST "${BASE_URL}${ENDPOINT}" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "searchKey=&pagenum=1&pagesize=3" | jq -r '.result.data[].id'

echo
echo "第3页前3条记录的ID:"
curl -s -X POST "${BASE_URL}${ENDPOINT}" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "searchKey=&pagenum=3&pagesize=3" | jq -r '.result.data[].id'

echo
echo "=== 测试完成 ==="
