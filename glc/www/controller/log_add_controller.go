package controller

import (
	"glc/com"
	"glc/conf"
	"glc/gweb"
	"glc/ldb"
	"glc/ldb/storage"
	"glc/ldb/storage/logdata"
	"sort"
	"sync"
	"time"

	"github.com/gotoeasy/glang/cmn"
)

var mapSystem = make(map[string]int64)
var muSystem sync.Mutex

var mapClientIp = make(map[string]int64)
var muClientIp sync.Mutex

var mapUser = make(map[string]int64)
var muUser sync.Mutex

var mapServerName = make(map[string]int64)
var muServerName sync.Mutex

// 添加日志（JSON数组提交方式）
func JsonLogAddBatchController(req *gweb.HttpRequest) *gweb.HttpResult {

	// 开启API秘钥校验时才检查
	if conf.IsEnableSecurityKey() && req.GetHeader(conf.GetHeaderSecurityKey()) != conf.GetSecurityKey() {
		return gweb.Error(403, "未经授权的访问，拒绝服务")
	}

	var mds []logdata.LogDataModel
	err := req.BindJSON(&mds)
	if err != nil {
		cmn.Error("请求参数有误", err)
		return gweb.Error500(err.Error())
	}

	for i := 0; i < len(mds); i++ {
		md := &mds[i]
		md.Text = cmn.Trim(md.Text)
		if md.Text != "" {
			addDataModelLog(md)
			if conf.IsClusterMode() {
				go TransferGlc(conf.LogTransferAdd, md.ToJson()) // 转发其他GLC服务
			}
		}
	}
	return gweb.Ok()

}

// 添加日志（JSON提交方式）
func JsonLogAddController(req *gweb.HttpRequest) *gweb.HttpResult {

	// 开启API秘钥校验时才检查
	if conf.IsEnableSecurityKey() && req.GetHeader(conf.GetHeaderSecurityKey()) != conf.GetSecurityKey() {
		return gweb.Error(403, "未经授权的访问，拒绝服务")
	}

	md := &logdata.LogDataModel{}
	err := req.BindJSON(md)
	if err != nil {
		cmn.Error("请求参数有误", err)
		return gweb.Error500(err.Error())
	}

	md.Text = cmn.Trim(md.Text)
	if md.Text != "" {
		addDataModelLog(md)
		if conf.IsClusterMode() {
			go TransferGlc(conf.LogTransferAdd, md.ToJson()) // 转发其他GLC服务
		}
	}

	return gweb.Ok()
}

// 添加日志（来自数据转发）
func JsonLogTransferAddController(req *gweb.HttpRequest) *gweb.HttpResult {

	// 开启API秘钥校验时才检查
	if conf.IsEnableSecurityKey() && req.GetHeader(conf.GetHeaderSecurityKey()) != conf.GetSecurityKey() {
		return gweb.Error(403, "未经授权的访问，拒绝服务")
	}

	md := &logdata.LogDataModel{}
	err := req.BindJSON(md)
	if err != nil {
		cmn.Error("请求参数有误", err)
		return gweb.Error500(err.Error())
	}

	md.Text = cmn.Trim(md.Text)
	addDataModelLog(md)
	// addTextLog(md)
	return gweb.Ok()
}

// 添加日志
func addDataModelLog(data *logdata.LogDataModel) {
	engine := ldb.NewDefaultEngine()

	// 按配置要求在IP字段上附加城市信息（当IP含空格时认为已附加过）
	if conf.IsIpAddCity() {
		if data.ClientIp != "" && !cmn.Contains(data.ClientIp, " ") {
			data.ClientIp = cmn.GetCityIp(data.ClientIp)
		}
		if data.ServerIp != "" && !cmn.Contains(data.ServerIp, " ") {
			data.ServerIp = cmn.GetCityIp(data.ServerIp)
		}
	}

	engine.AddLogDataModel(data)

	// 缓存系统名称备用查询
	if data.System != "" {
		if muSystem.TryLock() {
			defer muSystem.Unlock()
			mapSystem[data.System] = time.Now().UnixMilli()
		}
	}

	// 缓存客户端IP备用查询
	if data.ClientIp != "" {
		// 如果IP包含空格（城市信息），只取IP部分
		clientIp := data.ClientIp
		if cmn.Contains(clientIp, " ") {
			clientIp = cmn.Split(clientIp, " ")[0]
		}
		if muClientIp.TryLock() {
			defer muClientIp.Unlock()
			mapClientIp[clientIp] = time.Now().UnixMilli()
		}
	}

	// 缓存用户名备用查询
	if data.User != "" {
		if muUser.TryLock() {
			defer muUser.Unlock()
			mapUser[data.User] = time.Now().UnixMilli()
		}
	}

	// 缓存服务器名备用查询
	if data.ServerName != "" {
		if muServerName.TryLock() {
			defer muServerName.Unlock()
			mapServerName[data.ServerName] = time.Now().UnixMilli()
		}
	}

}

// 取近1天缓存的系统名称并清理
func GetAllSystemNames() []string {
	var mapSet = make(map[string]bool)
	var rs []string
	var dels []string
	now := time.Now().UnixMilli()
	muSystem.Lock()
	defer muSystem.Unlock()
	for key, value := range mapSystem {
		if now-value < 86400000 {
			tmp := cmn.ToLower(key)
			if _, has := mapSet[tmp]; !has {
				rs = append(rs, key) // 一天内
				mapSet[tmp] = true
			}
		} else {
			dels = append(dels, key) // 超过1天待删除
		}
	}
	for _, key := range dels {
		delete(mapSystem, key)
	}
	sort.Slice(rs, func(i, j int) bool {
		return rs[i] < rs[j]
	})
	return rs
}

// 取近1天缓存的客户端IP并清理
func GetAllClientIps() []string {
	var mapSet = make(map[string]bool)
	var rs []string
	var dels []string
	now := time.Now().UnixMilli()
	muClientIp.Lock()
	defer muClientIp.Unlock()
	for key, value := range mapClientIp {
		if now-value < 86400000 {
			tmp := cmn.ToLower(key)
			if _, has := mapSet[tmp]; !has {
				rs = append(rs, key) // 一天内
				mapSet[tmp] = true
			}
		} else {
			dels = append(dels, key) // 超过1天待删除
		}
	}
	for _, key := range dels {
		delete(mapClientIp, key)
	}
	sort.Slice(rs, func(i, j int) bool {
		return rs[i] < rs[j]
	})
	return rs
}

// 取近1天缓存的用户名并清理
func GetAllUsers() []string {
	var mapSet = make(map[string]bool)
	var rs []string
	var dels []string
	now := time.Now().UnixMilli()
	muUser.Lock()
	defer muUser.Unlock()
	for key, value := range mapUser {
		if now-value < 86400000 {
			tmp := cmn.ToLower(key)
			if _, has := mapSet[tmp]; !has {
				rs = append(rs, key) // 一天内
				mapSet[tmp] = true
			}
		} else {
			dels = append(dels, key) // 超过1天待删除
		}
	}
	for _, key := range dels {
		delete(mapUser, key)
	}
	sort.Slice(rs, func(i, j int) bool {
		return rs[i] < rs[j]
	})
	return rs
}

// 取近1天缓存的服务器名并清理
func GetAllServerNames() []string {
	var mapSet = make(map[string]bool)
	var rs []string
	var dels []string
	now := time.Now().UnixMilli()
	muServerName.Lock()
	defer muServerName.Unlock()
	for key, value := range mapServerName {
		if now-value < 86400000 {
			tmp := cmn.ToLower(key)
			if _, has := mapSet[tmp]; !has {
				rs = append(rs, key) // 一天内
				mapSet[tmp] = true
			}
		} else {
			dels = append(dels, key) // 超过1天待删除
		}
	}
	for _, key := range dels {
		delete(mapServerName, key)
	}
	sort.Slice(rs, func(i, j int) bool {
		return rs[i] < rs[j]
	})
	return rs
}

// 从存储中查询所有客户端IP（全量查询，性能较慢）
func GetAllClientIpsFromStorage() []string {
	return getAllFieldValuesFromStorage("clientip")
}

// 从存储中查询所有用户（全量查询，性能较慢）
func GetAllUsersFromStorage() []string {
	return getAllFieldValuesFromStorage("user")
}

// 从存储中查询所有服务器名（全量查询，性能较慢）
func GetAllServerNamesFromStorage() []string {
	return getAllFieldValuesFromStorage("servername")
}

// 从所有日志仓中提取指定字段的唯一值列表（全量查询，性能较慢）
func getAllFieldValuesFromStorage(fieldName string) []string {
	var mapSet = make(map[string]bool)
	var rs []string

	// 获取所有日志仓名称
	storeNames := com.GetStorageNames(conf.GetStorageRoot(), ".sysmnt")

	// 遍历每个日志仓
	for _, storeName := range storeNames {
		storeLogData := storage.NewLogDataStorageHandle(storeName)
		if storeLogData == nil {
			continue
		}

		totalCount := storeLogData.TotalCount()
		if totalCount == 0 {
			continue
		}

		// 遍历该日志仓中的所有日志记录
		// 为了避免内存占用过大，分批处理
		batchSize := uint32(1000)
		for start := uint32(1); start <= totalCount; start += batchSize {
			end := start + batchSize - 1
			if end > totalCount {
				end = totalCount
			}

			// 处理当前批次
			for i := start; i <= end; i++ {
				md := storeLogData.GetLogDataModel(i)
				if md == nil {
					continue // 跳过空记录
				}

				var value string
				switch fieldName {
				case "clientip":
					value = md.ClientIp
					// 如果IP包含空格（城市信息），只取IP部分
					if cmn.Contains(value, " ") {
						value = cmn.Split(value, " ")[0]
					}
				case "user":
					value = md.User
				case "servername":
					value = md.ServerName
				}

				// 添加到结果集（去重，忽略大小写）
				if value != "" {
					tmp := cmn.ToLower(value)
					if !mapSet[tmp] {
						mapSet[tmp] = true
						rs = append(rs, value)
					}
				}
			}
		}
	}

	// 排序
	sort.Slice(rs, func(i, j int) bool {
		return rs[i] < rs[j]
	})

	return rs
}
