# 分页跳转功能使用说明

## 🎯 **功能概述**

现在 `LogSearchController` 已经支持**页码跳转**功能！您可以直接从第1页跳转到第3页，无需逐页查询。

## 📋 **新增参数**

| 参数名 | 类型 | 说明 | 示例 | 默认值 |
|--------|------|------|------|--------|
| `pagenum` | int | 页码（从1开始） | `1`, `2`, `3` | `1` |
| `pagesize` | int | 每页显示条数 | `20`, `50`, `100` | 配置文件默认值 |

## 🚀 **使用方法**

### 方法1：直接跳转到第3页

```bash
# 直接查询第3页数据（每页20条）
curl -X POST "http://localhost:8080/glc/v1/log/search" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "searchKey=error&pagenum=3&pagesize=20"
```

### 方法2：JavaScript调用

```javascript
// 直接跳转到第3页
const searchPage = async (pageNum, pageSize = 20) => {
  const response = await $post('/v1/log/search', {
    searchKey: 'error',
    system: 'web-system',
    loglevel: 'error',
    pagenum: pageNum,      // 页码
    pagesize: pageSize     // 每页大小
  }, null, { 'Content-Type': 'application/x-www-form-urlencoded' });
  
  return response;
};

// 使用示例
searchPage(3, 20).then(result => {
  console.log('第3页数据:', result.result.data);
  console.log('总记录数:', result.result.total);
  console.log('当前页大小:', result.result.pagesize);
});
```

### 方法3：完整的分页查询示例

```javascript
// 完整的分页查询函数
const searchWithPagination = async (searchParams, pageNum = 1, pageSize = 20) => {
  const requestData = {
    ...searchParams,  // 搜索条件
    pagenum: pageNum, // 页码
    pagesize: pageSize // 每页大小
  };
  
  const response = await $post('/v1/log/search', requestData, null, {
    'Content-Type': 'application/x-www-form-urlencoded'
  });
  
  if (response.success) {
    const result = response.result;
    return {
      data: result.data,           // 当前页数据
      total: parseInt(result.total), // 总记录数
      pageSize: parseInt(result.pagesize), // 每页大小
      currentPage: pageNum,        // 当前页码
      totalPages: Math.ceil(parseInt(result.total) / pageSize), // 总页数
      timeMessage: result.timemessage // 查询耗时
    };
  }
  
  return null;
};

// 使用示例
const searchConditions = {
  searchKey: 'error',
  system: 'web-system',
  loglevel: 'error,warn',
  datetimeFrom: '2024-12-01 00:00:00',
  datetimeTo: '2024-12-04 23:59:59',
  user: 'admin',
  clientip: '*************'
};

// 查询第1页
searchWithPagination(searchConditions, 1, 20);

// 查询第3页
searchWithPagination(searchConditions, 3, 20);

// 查询第10页
searchWithPagination(searchConditions, 10, 50);
```

## 📊 **返回数据格式**

```json
{
  "success": true,
  "result": {
    "total": "1500",           // 总记录数
    "count": "1200",          // 当前条件匹配的记录数
    "pagesize": "20",         // 每页大小
    "data": [                 // 当前页数据
      {
        "id": 1041,
        "datetime": "2024-12-04 15:30:25",
        "system": "web-system",
        "loglevel": "ERROR",
        "text": "数据库连接失败",
        "user": "admin",
        "clientip": "*************",
        "storename": "logdata-20241204"
      }
      // ... 更多记录
    ],
    "laststorename": "logdata-20241204",
    "timemessage": "耗时 120 毫秒"
  }
}
```

## 🔧 **工作原理**

1. **跳过计算**: `skipCount = (pageNum - 1) * pageSize`
2. **记录跳过**: 在查询过程中跳过前面的记录
3. **智能处理**: 自动处理跨日志仓的分页
4. **性能优化**: 只查询需要的数据，不加载前面的页面

## ⚡ **性能特点**

| 查询方式 | 第1页 | 第3页 | 第10页 | 说明 |
|---------|-------|-------|--------|------|
| **跳转查询** | 快 | 中等 | 较慢 | 需要跳过前面的记录 |
| **滚动查询** | 快 | 快 | 快 | 基于游标，性能稳定 |

## 💡 **使用建议**

### 1. 选择合适的分页方式

```javascript
// 场景1：用户需要跳转到具体页码 → 使用页码跳转
searchWithPagination(conditions, 5, 20);

// 场景2：用户滚动浏览 → 使用滚动分页
searchMore(); // 现有的滚动加载功能
```

### 2. 合理设置页面大小

```javascript
// 小页面：适合跳转查询
searchWithPagination(conditions, 10, 20);

// 大页面：适合滚动查询  
searchWithPagination(conditions, 1, 100);
```

### 3. 添加加载提示

```javascript
const searchPageWithLoading = async (pageNum, pageSize) => {
  // 显示加载提示
  showLoading(`正在加载第${pageNum}页...`);
  
  try {
    const result = await searchWithPagination(conditions, pageNum, pageSize);
    return result;
  } finally {
    hideLoading();
  }
};
```

## 🎨 **前端分页组件示例**

```vue
<template>
  <div>
    <!-- 数据表格 -->
    <el-table :data="tableData" v-loading="loading">
      <!-- 表格列定义 -->
    </el-table>
    
    <!-- 分页组件 -->
    <el-pagination
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[20, 50, 100, 200]"
      :total="total"
      layout="total, sizes, prev, pager, next, jumper"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup>
const tableData = ref([]);
const loading = ref(false);
const currentPage = ref(1);
const pageSize = ref(20);
const total = ref(0);

// 页码改变
const handleCurrentChange = (page) => {
  searchPage(page, pageSize.value);
};

// 页面大小改变
const handleSizeChange = (size) => {
  pageSize.value = size;
  searchPage(1, size); // 重新从第1页开始
};

// 搜索指定页
const searchPage = async (page, size) => {
  loading.value = true;
  try {
    const result = await searchWithPagination(searchConditions, page, size);
    if (result) {
      tableData.value = result.data;
      total.value = result.total;
      currentPage.value = result.currentPage;
    }
  } finally {
    loading.value = false;
  }
};
</script>
```

## 🔍 **兼容性说明**

- ✅ **向后兼容**: 不传 `pagenum` 参数时，默认查询第1页
- ✅ **滚动分页**: 现有的滚动分页功能继续有效
- ✅ **相邻检索**: 相邻检索功能不受影响
- ✅ **跨仓查询**: 自动处理跨日志仓的页码跳转

## 🚨 **注意事项**

1. **页码从1开始**: `pagenum=1` 表示第1页
2. **合理范围**: 建议页码不要过大，避免性能问题
3. **数据一致性**: 在数据快速变化的环境中，页码跳转可能出现数据重复或遗漏
4. **权限控制**: 页码跳转遵循相同的权限控制规则

现在您可以轻松实现页码跳转功能了！🎉
